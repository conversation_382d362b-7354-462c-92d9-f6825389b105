import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { LinkData } from '@/types/admin';

interface ProfileSettingsProps {
  userInfo: LinkData['userInfo'];
  seo: LinkData['seo'];
  onUpdateUserInfo: (field: keyof LinkData['userInfo'], value: string) => void;
  onUpdateSEO: (field: keyof LinkData['seo'], value: string) => void;
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({
  userInfo,
  seo,
  onUpdateUserInfo,
  onUpdateSEO,
}) => {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Informações do Perfil</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Nome</Label>
            <Input
              id="name"
              value={userInfo.name}
              onChange={(e) => onUpdateUserInfo('name', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={userInfo.bio}
              onChange={(e) => onUpdateUserInfo('bio', e.target.value)}
              rows={3}
            />
          </div>
          <div>
            <Label htmlFor="avatar">URL do Avatar</Label>
            <Input
              id="avatar"
              value={userInfo.avatar}
              onChange={(e) => onUpdateUserInfo('avatar', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>SEO</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="seoTitle">Título</Label>
            <Input
              id="seoTitle"
              value={seo.title}
              onChange={(e) => onUpdateSEO('title', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="seoDescription">Descrição</Label>
            <Textarea
              id="seoDescription"
              value={seo.description}
              onChange={(e) => onUpdateSEO('description', e.target.value)}
              rows={2}
            />
          </div>
          <div>
            <Label htmlFor="seoKeywords">Palavras-chave</Label>
            <Input
              id="seoKeywords"
              value={seo.keywords}
              onChange={(e) => onUpdateSEO('keywords', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSettings;
