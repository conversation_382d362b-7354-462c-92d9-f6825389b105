import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Links Page Editor",
  description:
    "Modern links page editor built using Next.js, Tailwind CSS, TypeScript.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased bg-background text-foreground">
        {children}
      </body>
    </html>
  );
}
