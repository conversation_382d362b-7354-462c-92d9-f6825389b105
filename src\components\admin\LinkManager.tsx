import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { iconOptions } from '@/constants/admin';
import { LinkItem } from '@/types/admin';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2 } from 'lucide-react';

interface LinkManagerProps {
  links: LinkItem[];
  onAddLink: () => void;
  onRemoveLink: (id: string) => void;
  onUpdateLink: (id: string, field: keyof LinkItem, value: any) => void;
}

const LinkManager: React.FC<LinkManagerProps> = ({ links, onAddLink, onRemoveLink, onUpdateLink }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Links
          <Button onClick={onAddLink} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Adicionar Link
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {links.map((link) => (
          <div key={link.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Switch
                checked={link.enabled}
                onCheckedChange={(checked) => onUpdateLink(link.id, "enabled", checked)}
              />
              <Button onClick={() => onRemoveLink(link.id)} variant="outline" size="sm">
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div>
              <Label>Título</Label>
              <Input
                value={link.title}
                onChange={(e) => onUpdateLink(link.id, "title", e.target.value)}
              />
            </div>
            <div>
              <Label>URL</Label>
              <Input
                value={link.url}
                onChange={(e) => onUpdateLink(link.id, "url", e.target.value)}
              />
            </div>
            <div>
              <Label>Ícone</Label>
              <Select
                value={link.icon}
                onValueChange={(value) => onUpdateLink(link.id, "icon", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {iconOptions.map((icon) => (
                    <SelectItem key={icon} value={icon}>
                      {icon}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
        {links.length === 0 && (
          <p className="text-center text-gray-500 py-8">
            Nenhum link adicionado. Clique em "Adicionar Link" para começar.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default LinkManager;
