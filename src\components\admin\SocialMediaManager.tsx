import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { iconOptions } from '@/constants/admin';
import { SocialMediaItem } from '@/types/admin';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2 } from 'lucide-react';

interface SocialMediaManagerProps {
  socialMedia: SocialMediaItem[];
  onAddSocialMedia: () => void;
  onRemoveSocialMedia: (id: string) => void;
  onUpdateSocialMedia: (id: string, field: keyof SocialMediaItem, value: any) => void;
}

const SocialMediaManager: React.FC<SocialMediaManagerProps> = ({
  socialMedia,
  onAddSocialMedia,
  onRemoveSocialMedia,
  onUpdateSocialMedia,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Redes Sociais
          <Button onClick={onAddSocialMedia} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Adicionar Rede
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {socialMedia.map((social) => (
          <div key={social.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <Switch
                checked={social.enabled}
                onCheckedChange={(checked) => onUpdateSocialMedia(social.id, "enabled", checked)}
              />
              <Button onClick={() => onRemoveSocialMedia(social.id)} variant="outline" size="sm">
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div>
              <Label>Plataforma</Label>
              <Input
                value={social.platform}
                onChange={(e) => onUpdateSocialMedia(social.id, "platform", e.target.value)}
              />
            </div>
            <div>
              <Label>URL</Label>
              <Input
                value={social.url}
                onChange={(e) => onUpdateSocialMedia(social.id, "url", e.target.value)}
              />
            </div>
            <div>
              <Label>Ícone</Label>
              <Select
                value={social.icon}
                onValueChange={(value) => onUpdateSocialMedia(social.id, "icon", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {iconOptions.map((icon) => (
                    <SelectItem key={icon} value={icon}>
                      {icon}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
        {socialMedia.length === 0 && (
          <p className="text-center text-gray-500 py-8">
            Nenhuma rede social adicionada. Clique em "Adicionar Rede" para começar.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default SocialMediaManager;
