"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Link,
  Github,
  Linkedin,
  Twitter,
  Instagram,
  Youtube,
  FileText,
  Briefcase,
  ExternalLink,
} from "lucide-react";

interface LinkItem {
  id: string;
  title: string;
  url: string;
  icon?: string;
  enabled: boolean;
}

interface SocialMediaItem {
  id: string;
  platform: string;
  url: string;
  icon: string;
  enabled: boolean;
}

interface UserInfo {
  name: string;
  bio: string;
  avatar: string;
}

interface Styles {
  theme: string;
  colors: {
    background: string;
    text: string;
    primary: string;
    primaryHover: string;
    cardBackground: string;
    border: string;
  };
  typography: {
    fontFamily: string;
    fontSize: string;
    headingSize: string;
  };
  layout: {
    borderRadius: string;
    spacing: string;
    maxWidth: string;
  };
  background: {
    type: string;
    value: string;
    image: string;
  };
}

interface EnhancedPreviewProps {
  data: {
    userInfo: UserInfo;
    links: LinkItem[];
    socialMedia: SocialMediaItem[];
    styles: Styles;
  };
}

const iconComponents: { [key: string]: React.ComponentType<any> } = {
  Link,
  Github,
  Linkedin,
  Twitter,
  Instagram,
  Youtube,
  FileText,
  Briefcase,
  ExternalLink,
};

export default function EnhancedPreview({ data }: EnhancedPreviewProps) {
  const { userInfo, links, socialMedia, styles } = data;

  const getIconComponent = (iconName: string) => {
    return iconComponents[iconName] || Link;
  };

  const enabledLinks = links.filter((link) => link.enabled);
  const enabledSocialMedia = socialMedia.filter((social) => social.enabled);

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            Preview
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            {enabledLinks.length} links • {enabledSocialMedia.length} redes
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="h-full overflow-auto">
          {/* Device Frame */}
          <div className="p-4 bg-gray-100">
            <div className="mx-auto max-w-xs">
              {/* Device Header */}
              <div className="bg-black rounded-t-3xl p-2">
                <div className="bg-gray-800 h-6 rounded-full mx-auto w-32 flex items-center justify-center">
                  <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                </div>
              </div>

              {/* Screen Content */}
              <div
                className="min-h-[600px] p-4 transition-all duration-300"
                style={{
                  backgroundColor: styles.colors.background,
                  backgroundImage:
                    styles.background.type === "image"
                      ? `url(${styles.background.image})`
                      : "none",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  fontFamily: styles.typography.fontFamily,
                }}
              >
                <div
                  className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 space-y-6"
                  style={{
                    backgroundColor: styles.colors.cardBackground,
                    borderColor: styles.colors.border,
                    borderRadius: styles.layout.borderRadius,
                  }}
                >
                  {/* Profile Section */}
                  <div className="text-center space-y-4">
                    <div className="relative">
                      <Avatar className="w-20 h-20 mx-auto ring-4 ring-white/50">
                        <AvatarImage
                          src={userInfo.avatar}
                          alt={userInfo.name}
                        />
                        <AvatarFallback className="text-xl font-bold">
                          {userInfo.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>

                    <div className="space-y-1">
                      <h2
                        className="text-xl font-bold"
                        style={{ color: styles.colors.text }}
                      >
                        {userInfo.name || "Seu Nome"}
                      </h2>
                      <p
                        className="text-sm leading-relaxed"
                        style={{ color: styles.colors.text }}
                      >
                        {userInfo.bio || "Sua bio aparecerá aqui..."}
                      </p>
                    </div>
                  </div>

                  <Separator
                    style={{ backgroundColor: styles.colors.border }}
                  />

                  {/* Links Section */}
                  <div className="space-y-3">
                    {enabledLinks.length > 0 ? (
                      enabledLinks.map((link) => {
                        const Icon = getIconComponent(link.icon || "Link");
                        return (
                          <button
                            key={link.id}
                            className="w-full p-3 text-left rounded-xl border-2 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] flex items-center gap-3 group"
                            style={{
                              backgroundColor: styles.colors.cardBackground,
                              borderColor: styles.colors.border,
                              color: styles.colors.text,
                            }}
                          >
                            <Icon className="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform" />
                            <span className="font-medium flex-1 truncate">
                              {link.title}
                            </span>
                            <ExternalLink className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity" />
                          </button>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-gray-400 text-sm">
                          Adicione links para vê-los aqui
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Social Media Section */}
                  {enabledSocialMedia.length > 0 && (
                    <>
                      <Separator
                        style={{ backgroundColor: styles.colors.border }}
                      />
                      <div className="space-y-3">
                        <h3
                          className="text-xs font-semibold text-center uppercase tracking-wider opacity-70"
                          style={{ color: styles.colors.text }}
                        >
                          Redes Sociais
                        </h3>
                        <div className="flex justify-center gap-2 flex-wrap">
                          {enabledSocialMedia.map((social) => {
                            const Icon = getIconComponent(social.icon);
                            return (
                              <button
                                key={social.id}
                                className="p-2 rounded-xl border-2 transition-all duration-200 hover:scale-110"
                                style={{
                                  backgroundColor: styles.colors.cardBackground,
                                  borderColor: styles.colors.border,
                                  color: styles.colors.text,
                                }}
                                title={social.platform}
                              >
                                <Icon className="w-5 h-5" />
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    </>
                  )}

                  {/* Footer */}
                  <div className="flex justify-center gap-3 pt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs opacity-70"
                      style={{ color: styles.colors.text }}
                      asChild
                    >
                      <a href="/privacy">Privacidade</a>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs opacity-70"
                      style={{ color: styles.colors.text }}
                      asChild
                    >
                      <a href="/terms">Termos</a>
                    </Button>
                  </div>
                </div>
              </div>

              {/* Device Footer */}
              <div className="bg-black rounded-b-3xl p-2">
                <div className="bg-gray-800 h-1 rounded-full mx-auto w-20"></div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
