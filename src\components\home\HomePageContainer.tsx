import React from "react";
import HomeHeader from "./HomeHeader";
import ActionButtons from "../ui/ActionButtons";
import { useLinkPageData } from "@/hooks/useLinkPageData";

const HomePageContainer = () => {
  const { fileInputRef, loadFile, createNew, handleFileChange } =
    useLinkPageData();

  return (
    <div className="min-h-screen w-full bg-[#fefcff] relative">
      {/* Dreamy Sky Pink Glow */}
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `
            radial-gradient(circle at 30% 70%, rgba(173, 216, 230, 0.35), transparent 60%),
            radial-gradient(circle at 70% 30%, rgba(255, 182, 193, 0.4), transparent 60%)`,
        }}
      />
      <div className="absolute inset-0 flex items-center justify-center p-4">
        <div className="text-center max-w-xl">
          <HomeHeader />
          <ActionButtons onLoadData={loadFile} onCreateNew={createNew} />
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
        </div>
      </div>
    </div>
  );
};

export default HomePageContainer;
