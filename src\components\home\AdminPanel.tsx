import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AdminPanelProps {
  showAdmin: boolean;
  filename: string;
  onFilenameChange: (filename: string) => void;
  onLoadData: () => void;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ 
  showAdmin, 
  filename, 
  onFilenameChange, 
  onLoadData 
}) => {
  if (!showAdmin) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-40 flex items-center justify-center p-4">
      <Card className="w-full max-w-md max-h-[80vh] overflow-y-auto">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">Admin Panel</h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="filename">JSON Filename</Label>
              <Input
                id="filename"
                value={filename}
                onChange={(e) => onFilenameChange(e.target.value)}
                placeholder="Enter filename (e.g., sample.json)"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={onLoadData} className="flex-1">
                Load Data
              </Button>
              <Button
                variant="outline"
                onClick={() => (window.location.href = "/admin")}
                className="flex-1"
              >
                Full Editor
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPanel;
