import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Styles } from '@/types/admin';

interface LayoutSettingsProps {
  layout: Styles['layout'];
  onUpdateLayout: (field: keyof Styles['layout'], value: string) => void;
}

const LayoutSettings: React.FC<LayoutSettingsProps> = ({ layout, onUpdateLayout }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Layout</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="maxWidth">Largura <PERSON>xima</Label>
          <Select
            value={layout.maxWidth}
            onValueChange={(value) => onUpdateLayout('maxWidth', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="320px">320px</SelectItem>
              <SelectItem value="400px">400px</SelectItem>
              <SelectItem value="480px">480px</SelectItem>
              <SelectItem value="600px">600px</SelectItem>
              <SelectItem value="800px">800px</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="borderRadius">Raio da Borda</Label>
          <Select
            value={layout.borderRadius}
            onValueChange={(value) => onUpdateLayout('borderRadius', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0px">0px</SelectItem>
              <SelectItem value="4px">4px</SelectItem>
              <SelectItem value="8px">8px</SelectItem>
              <SelectItem value="12px">12px</SelectItem>
              <SelectItem value="16px">16px</SelectItem>
              <SelectItem value="24px">24px</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="spacing">Espaçamento</Label>
          <Select
            value={layout.spacing}
            onValueChange={(value) => onUpdateLayout('spacing', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="8px">8px</SelectItem>
              <SelectItem value="12px">12px</SelectItem>
              <SelectItem value="16px">16px</SelectItem>
              <SelectItem value="20px">20px</SelectItem>
              <SelectItem value="24px">24px</SelectItem>
              <SelectItem value="32px">32px</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};

export default LayoutSettings;
