import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { themes } from '@/constants/admin';
import { Styles } from '@/types/admin';

interface StyleEditorProps {
  styles: Styles;
  onApplyTheme: (theme: string) => void;
  onUpdateStyle: (category: keyof Styles['colors'], value: string) => void;
}

const StyleEditor: React.FC<StyleEditorProps> = ({ styles, onApplyTheme, onUpdateStyle }) => {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Temas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {themes.map((theme) => (
              <button
                key={theme.value}
                onClick={() => onApplyTheme(theme.value)}
                className={`p-4 border rounded-lg text-left transition-all ${
                  styles.theme === theme.value
                    ? 'ring-2 ring-blue-500 border-blue-500'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium">{theme.name}</div>
                <div className="flex gap-1 mt-2">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.background }}
                  />
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.primary }}
                  />
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: theme.colors.text }}
                  />
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Cores Personalizadas</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(styles.colors).map(([key, value]) => (
             <div key={key}>
               <Label className="text-sm capitalize">
                 {key.replace(/([A-Z])/g, ' $1').trim()}
               </Label>
               <div className="flex gap-2 mt-1">
                 <input
                   type="color"
                   value={value}
                   onChange={(e) =>
                     onUpdateStyle(key as keyof Styles['colors'], e.target.value)
                   }
                   className="w-10 h-10 border rounded cursor-pointer"
                 />
                 <Input
                   value={value}
                   onChange={(e) =>
                     onUpdateStyle(key as keyof Styles['colors'], e.target.value)
                   }
                   className="flex-1"
                 />
               </div>
             </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StyleEditor;
