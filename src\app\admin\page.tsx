"use client";

import { TabsContent } from "@/components/ui/tabs";
import { useAdminData } from "@/hooks/use-admin-data";
import AdminSidebar from "@/components/admin/AdminSidebar";
import AdminToolbar from "@/components/admin/AdminToolbar";
import ProfileSettings from "@/components/admin/ProfileSettings";
import LinkManager from "@/components/admin/LinkManager";
import SocialMediaManager from "@/components/admin/SocialMediaManager";
import StyleEditor from "@/components/admin/StyleEditor";
import LayoutSettings from "@/components/admin/LayoutSettings";
import PageSettings from "@/components/admin/PageSettings";
import EnhancedPreview from "@/components/admin/EnhancedPreview";

export default function AdminPage() {
  const {
    linkData,
    showPreview,
    isLoading,
    setShowPreview,
    saveData,
    exportData,
    importData,
    applyTheme,
    setLinkData,
  } = useAdminData("sample.json");

  const addLink = () => {
    const newLink = {
      id: Date.now().toString(),
      title: "",
      url: "",
      icon: "Link",
      enabled: true,
    };
    setLinkData((prev) => ({
      ...prev,
      links: [...prev.links, newLink],
    }));
  };

  const removeLink = (id: string) => {
    setLinkData((prev) => ({
      ...prev,
      links: prev.links.filter((link) => link.id !== id),
    }));
  };

  const addSocialMedia = () => {
    const newSocial = {
      id: Date.now().toString(),
      platform: "",
      url: "",
      icon: "Share2",
      enabled: true,
    };
    setLinkData((prev) => ({
      ...prev,
      socialMedia: [...prev.socialMedia, newSocial],
    }));
  };

  const removeSocialMedia = (id: string) => {
    setLinkData((prev) => ({
      ...prev,
      socialMedia: prev.socialMedia.filter((social) => social.id !== id),
    }));
  };

  const updateLink = (id: string, field: string, value: any) => {
    setLinkData((prev) => ({
      ...prev,
      links: prev.links.map((link) =>
        link.id === id ? { ...link, [field]: value } : link
      ),
    }));
  };

  const updateSocialMedia = (id: string, field: string, value: any) => {
    setLinkData((prev) => ({
      ...prev,
      socialMedia: prev.socialMedia.map((social) =>
        social.id === id ? { ...social, [field]: value } : social
      ),
    }));
  };

  const updateUserInfo = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      userInfo: {
        ...prev.userInfo,
        [field]: value,
      },
    }));
  };

  const updateSEO = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      seo: {
        ...prev.seo,
        [field]: value,
      },
    }));
  };

  const updateStyleColor = (category: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      styles: {
        ...prev.styles,
        colors: {
          ...prev.styles.colors,
          [category]: value,
        },
      },
    }));
  };

  const updateLayout = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      styles: {
        ...prev.styles,
        layout: {
          ...prev.styles.layout,
          [field]: value,
        },
      },
    }));
  };

  const updatePage = (page: string, field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      pages: {
        ...prev.pages,
        [page]: {
          ...prev.pages[page as keyof typeof prev.pages],
          [field]: value,
        },
      },
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar
        onShowPreview={() => setShowPreview(!showPreview)}
        onExportData={exportData}
        onImportData={importData}
        onSaveData={saveData}
        isPreviewVisible={showPreview}
      />

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            <AdminToolbar>
              <TabsContent value="userInfo" className="space-y-4">
                <ProfileSettings
                  userInfo={linkData.userInfo}
                  seo={linkData.seo}
                  onUpdateUserInfo={updateUserInfo}
                  onUpdateSEO={updateSEO}
                />
              </TabsContent>

              <TabsContent value="links" className="space-y-4">
                <LinkManager
                  links={linkData.links}
                  onAddLink={addLink}
                  onRemoveLink={removeLink}
                  onUpdateLink={updateLink}
                />
              </TabsContent>

              <TabsContent value="social" className="space-y-4">
                <SocialMediaManager
                  socialMedia={linkData.socialMedia}
                  onAddSocialMedia={addSocialMedia}
                  onRemoveSocialMedia={removeSocialMedia}
                  onUpdateSocialMedia={updateSocialMedia}
                />
              </TabsContent>

              <TabsContent value="styles" className="space-y-4">
                <div className="grid grid-cols-1 gap-6">
                  <StyleEditor
                    styles={linkData.styles}
                    onApplyTheme={applyTheme}
                    onUpdateStyle={updateStyleColor}
                  />
                  <LayoutSettings
                    layout={linkData.styles.layout}
                    onUpdateLayout={updateLayout}
                  />
                </div>
              </TabsContent>

              <TabsContent value="pages" className="space-y-4">
                <PageSettings
                  pages={linkData.pages}
                  onUpdatePage={updatePage}
                />
              </TabsContent>
            </AdminToolbar>
          </div>

          {showPreview && (
            <div className="lg:sticky lg:top-6 lg:h-fit">
              <EnhancedPreview
                data={{
                  userInfo: linkData.userInfo,
                  links: linkData.links,
                  socialMedia: linkData.socialMedia,
                  styles: linkData.styles,
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
