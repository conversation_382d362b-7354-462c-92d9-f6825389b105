import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Pages } from '@/types/admin';

interface PageSettingsProps {
  pages: Pages;
  onUpdatePage: (
    page: keyof Pages,
    field: 'title' | 'content',
    value: string
  ) => void;
}

const PageSettings: React.FC<PageSettingsProps> = ({ pages, onUpdatePage }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Páginas Legais</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label htmlFor="privacyTitle">
            Título da Política de Privacidade
          </Label>
          <Input
            id="privacyTitle"
            value={pages.privacy.title}
            onChange={(e) => onUpdatePage('privacy', 'title', e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="privacyContent">
            Conteúdo da Política de Privacidade
          </Label>
          <Textarea
            id="privacyContent"
            value={pages.privacy.content}
            onChange={(e) => onUpdatePage('privacy', 'content', e.target.value)}
            rows={8}
          />
        </div>
        <div>
          <Label htmlFor="termsTitle">
            Título dos Termos de Uso
          </Label>
          <Input
            id="termsTitle"
            value={pages.terms.title}
            onChange={(e) => onUpdatePage('terms', 'title', e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="termsContent">
            Conteúdo dos Termos de Uso
          </Label>
          <Textarea
            id="termsContent"
            value={pages.terms.content}
            onChange={(e) => onUpdatePage('terms', 'content', e.target.value)}
            rows={8}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default PageSettings;
