import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface AdminSidebarProps {
  onShowPreview: () => void;
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSaveData: () => void;
  isPreviewVisible: boolean;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  onShowPreview,
  onExportData,
  onImportData,
  onSaveData,
  isPreviewVisible,
}) => {
  return (
    <div className="border-b bg-white">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button asChild>
              <Link href="/">Voltar</Link>
            </Button>
            <h1 className="text-xl font-bold">🎨 Editor de Links</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={onShowPreview} variant="outline">
              {isPreviewVisible ? "Ocultar" : "Ver"} Preview
            </Button>
            <Button onClick={onExportData} variant="outline">Exportar</Button>
            <label className="cursor-pointer">
              <Button variant="outline" asChild>
                <span className="flex items-center">
                  Importar
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={onImportData}
                className="hidden"
              />
            </label>
            <Button onClick={onSaveData}>Salvar</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
