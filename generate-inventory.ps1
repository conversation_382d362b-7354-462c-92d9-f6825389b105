# Generate Component & Page Inventory Script
# This script walks through src/components and src/app directories
# to list all components, pages, hooks, and utilities

$inventory = @{
    Components = @()
    Pages = @()
    Hooks = @()
    Utils = @()
    Others = @()
}

# Function to categorize files
function Get-FileCategory {
    param($filePath)
    
    $fileName = Split-Path -Path $filePath -Leaf
    $fileContent = Get-Content -Path $filePath -Raw -ErrorAction SilentlyContinue
    
    # Check if it's a hook (starts with use or contains custom hook pattern)
    if ($fileName -match '^use[A-Z].*\.(ts|tsx|js|jsx)$' -or 
        ($fileContent -match 'export\s+(default\s+)?function\s+use[A-Z]' -or 
         $fileContent -match 'export\s+const\s+use[A-Z]')) {
        return "Hooks"
    }
    # Check if it's a utility file
    elseif ($fileName -match '(util|utils|helper|helpers)\.(ts|tsx|js|jsx)$' -or
            $filePath -match '[\\/](util|utils|helper|helpers)[\\/]') {
        return "Utils"
    }
    # Check if it's a page (in app directory with page.tsx/js)
    elseif ($filePath -match '[\\/]app[\\/]' -and $fileName -match '^page\.(tsx|ts|jsx|js)$') {
        return "Pages"
    }
    # Check if it's a component
    elseif ($fileName -match '\.(tsx|jsx)$' -and 
            ($fileContent -match 'export\s+(default\s+)?function\s+[A-Z]' -or 
             $fileContent -match 'export\s+const\s+[A-Z].*=.*=>')) {
        return "Components"
    }
    # Default to Others
    else {
        return "Others"
    }
}

# Function to get relative path
function Get-RelativePath {
    param($fullPath, $basePath)
    
    $relativePath = $fullPath.Replace($basePath, "").TrimStart("\", "/")
    return $relativePath.Replace("\", "/")
}

# Get the base directory
$baseDir = Get-Location

# Process src/components directory
Write-Host "Scanning src/components directory..." -ForegroundColor Cyan
if (Test-Path "src/components") {
    $componentFiles = Get-ChildItem -Path "src/components" -Recurse -Include "*.tsx", "*.ts", "*.jsx", "*.js" -File
    foreach ($file in $componentFiles) {
        $category = Get-FileCategory -filePath $file.FullName
        $relativePath = Get-RelativePath -fullPath $file.FullName -basePath $baseDir
        $inventory[$category] += $relativePath
    }
}

# Process src/app directory
Write-Host "Scanning src/app directory..." -ForegroundColor Cyan
if (Test-Path "src/app") {
    $appFiles = Get-ChildItem -Path "src/app" -Recurse -Include "*.tsx", "*.ts", "*.jsx", "*.js" -File
    foreach ($file in $appFiles) {
        $category = Get-FileCategory -filePath $file.FullName
        $relativePath = Get-RelativePath -fullPath $file.FullName -basePath $baseDir
        $inventory[$category] += $relativePath
    }
}

# Generate markdown output
$markdownContent = @"
# REFACTORING_PLAN

## Inventory

### Components
"@

foreach ($component in ($inventory.Components | Sort-Object)) {
    $markdownContent += "`n- [ ] ``$component``"
}

$markdownContent += "`n`n### Pages"
foreach ($page in ($inventory.Pages | Sort-Object)) {
    $markdownContent += "`n- [ ] ``$page``"
}

$markdownContent += "`n`n### Hooks"
foreach ($hook in ($inventory.Hooks | Sort-Object)) {
    $markdownContent += "`n- [ ] ``$hook``"
}

$markdownContent += "`n`n### Utilities"
foreach ($util in ($inventory.Utils | Sort-Object)) {
    $markdownContent += "`n- [ ] ``$util``"
}

$markdownContent += "`n`n### Other Files"
foreach ($other in ($inventory.Others | Sort-Object)) {
    $markdownContent += "`n- [ ] ``$other``"
}

# Add summary
$markdownContent += @"

## Summary
- Total Components: $($inventory.Components.Count)
- Total Pages: $($inventory.Pages.Count)
- Total Hooks: $($inventory.Hooks.Count)
- Total Utilities: $($inventory.Utils.Count)
- Other Files: $($inventory.Others.Count)
- **Total Files**: $(($inventory.Components + $inventory.Pages + $inventory.Hooks + $inventory.Utils + $inventory.Others).Count)

Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@

# Write to file
$markdownContent | Out-File -FilePath "REFACTORING_PLAN.md" -Encoding UTF8

Write-Host "`nInventory generated successfully!" -ForegroundColor Green
Write-Host "Results saved to: REFACTORING_PLAN.md" -ForegroundColor Yellow

# Display summary
Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "- Components: $($inventory.Components.Count)" -ForegroundColor White
Write-Host "- Pages: $($inventory.Pages.Count)" -ForegroundColor White
Write-Host "- Hooks: $($inventory.Hooks.Count)" -ForegroundColor White
Write-Host "- Utilities: $($inventory.Utils.Count)" -ForegroundColor White
Write-Host "- Other Files: $($inventory.Others.Count)" -ForegroundColor White
