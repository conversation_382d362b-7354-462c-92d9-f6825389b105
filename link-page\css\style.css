/* Reset e Variáveis CSS */
:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #8b5cf6;
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 0.75rem;
    --transition: all 0.3s ease;
}

/* Tema Escuro */
[data-theme="dark"] {
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #334155;
}

/* Reset Global */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Container */
.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navegação */
.navbar {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(248, 250, 252, 0.8);
    transition: var(--transition);
}

[data-theme="dark"] .navbar {
    background-color: rgba(30, 41, 59, 0.8);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.nav-logo a:hover {
    color: var(--primary-hover);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

/* Conteúdo Principal */
.main-content {
    flex: 1;
    padding: 2rem 0;
}

.page {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Seção de Perfil */
.profile-section {
    text-align: center;
    padding: 2rem 0;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    border: 4px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    background-color: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar i {
    font-size: 3rem;
    color: var(--primary-color);
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.profile-bio {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Seção de Links */
.links-section {
    margin-bottom: 3rem;
}

.link-item {
    display: block;
    width: 100%;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.link-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.link-item:active {
    transform: translateY(0);
}

.link-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.link-item:hover::before {
    left: 100%;
}

/* Seção de Redes Sociais */
.social-section {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.2rem;
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Conteúdo Legal */
.legal-content {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.legal-content h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.legal-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
    color: var(--text-primary);
}

.legal-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 1.5rem 0 0.5rem 0;
    color: var(--text-primary);
}

.legal-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    line-height: 1.8;
}

.legal-content ul {
    margin-left: 2rem;
    margin-bottom: 1rem;
}

.legal-content li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.legal-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.legal-content a:hover {
    text-decoration: underline;
}

/* Rodapé */
.footer {
    background-color: var(--background-color);
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    text-align: center;
}

.footer-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
}

.footer-links a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .nav-menu {
        gap: 1rem;
    }

    .nav-link {
        font-size: 0.9rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .profile-bio {
        font-size: 1rem;
    }

    .social-section {
        gap: 0.75rem;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .legal-content {
        padding: 1.5rem;
    }

    .legal-content h1 {
        font-size: 1.5rem;
    }

    .legal-content h2 {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        gap: 1.5rem;
    }

    .container {
        padding: 0 0.75rem;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
    }

    .link-item {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }

    .social-section {
        gap: 0.5rem;
    }

    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-links span {
        display: none;
    }
}

/* Animações Adicionais */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para Temas Personalizados */
.theme-modern {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --border-radius: 0.5rem;
}

.theme-rounded {
    --primary-color: #ec4899;
    --primary-hover: #db2777;
    --border-radius: 2rem;
}

.theme-minimal {
    --primary-color: #374151;
    --primary-hover: #1f2937;
    --border-radius: 0.25rem;
}

.theme-vibrant {
    --primary-color: #f59e0b;
    --primary-hover: #d97706;
    --border-radius: 1rem;
}

/* Efeitos de Hover Avançados */
.link-item.gradient-hover {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
}

.link-item.gradient-hover:hover {
    background: linear-gradient(45deg, var(--primary-hover), var(--secondary-color));
    transform: translateY(-3px) scale(1.02);
}

/* Scrollbar Personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}