export interface LinkPageData {
  userInfo: {
    name: string;
    bio: string;
    avatar: string;
  };
  links: Array<{
    id: string;
    title: string;
    url: string;
    icon?: string;
    enabled: boolean;
  }>;
  socialMedia: Array<{
    id: string;
    platform: string;
    url: string;
    icon: string;
    enabled: boolean;
  }>;
  styles: {
    theme: string;
    colors: {
      background: string;
      text: string;
      primary: string;
      primaryHover: string;
      cardBackground: string;
      border: string;
    };
    typography: {
      fontFamily: string;
      fontSize: string;
      headingSize: string;
    };
    layout: {
      borderRadius: string;
      spacing: string;
      maxWidth: string;
    };
    background: {
      type: string;
      value: string;
      image: string;
    };
  };
  seo: {
    title: string;
    description: string;
    keywords: string;
    favicon: string;
    shareImage: string;
  };
}
