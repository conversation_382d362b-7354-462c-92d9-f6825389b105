import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

interface AdminToggleProps {
  showAdmin: boolean;
  onToggle: () => void;
}

const AdminToggle: React.FC<AdminToggleProps> = ({ showAdmin, onToggle }) => {
  return (
    <div className="fixed top-4 right-4 z-50">
      <Button
        variant="outline"
        size="sm"
        onClick={onToggle}
        className="gap-2"
      >
        {showAdmin ? (
          <Eye className="w-4 h-4" />
        ) : (
          <Settings className="w-4 h-4" />
        )}
        <span className="hidden sm:inline">
          {showAdmin ? "View Page" : "Admin Panel"}
        </span>
      </Button>
    </div>
  );
};

export default AdminToggle;
