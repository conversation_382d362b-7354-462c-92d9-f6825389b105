import { Theme } from '@/types/admin';

export const themes: Theme[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    value: "light",
    colors: {
      background: "#ffffff",
      text: "#1f2937",
      primary: "#3b82f6",
      primaryHover: "#2563eb",
      cardBackground: "#f9fafb",
      border: "#e5e7eb",
    },
  },
  {
    name: "Escuro",
    value: "dark",
    colors: {
      background: "#1f2937",
      text: "#f9fafb",
      primary: "#60a5fa",
      primaryHover: "#3b82f6",
      cardBackground: "#374151",
      border: "#4b5563",
    },
  },
  {
    name: "Azul",
    value: "blue",
    colors: {
      background: "#eff6ff",
      text: "#1e3a8a",
      primary: "#2563eb",
      primaryHover: "#1d4ed8",
      cardBackground: "#dbeafe",
      border: "#bfdbfe",
    },
  },
  {
    name: "<PERSON>",
    value: "green",
    colors: {
      background: "#f0fdf4",
      text: "#14532d",
      primary: "#16a34a",
      primaryHover: "#15803d",
      cardBackground: "#dcfce7",
      border: "#bbf7d0",
    },
  },
  {
    name: "Azul Bebê",
    value: "baby-blue",
    colors: {
      background: "#f0f9ff",
      text: "#1e40af",
      primary: "#3b82f6",
      primaryHover: "#2563eb",
      cardBackground: "#dbeafe",
      border: "#bfdbfe",
    },
  },
  {
    name: "Lavanda",
    value: "lavender",
    colors: {
      background: "#f5f3ff",
      text: "#5b21b6",
      primary: "#8b5cf6",
      primaryHover: "#7c3aed",
      cardBackground: "#ede9fe",
      border: "#ddd6fe",
    },
  },
  {
    name: "Verde Menta",
    value: "mint",
    colors: {
      background: "#f0fdf4",
      text: "#065f46",
      primary: "#10b981",
      primaryHover: "#059669",
      cardBackground: "#d1fae5",
      border: "#bbf7d0",
    },
  },
  {
    name: "Pêssego",
    value: "peach",
    colors: {
      background: "#fff7ed",
      text: "#7c2d12",
      primary: "#f97316",
      primaryHover: "#ea580c",
      cardBackground: "#ffedd5",
      border: "#fed7aa",
    },
  },
  {
    name: "Amarelo Claro",
    value: "light-yellow",
    colors: {
      background: "#fefce8",
      text: "#854d0e",
      primary: "#eab308",
      primaryHover: "#ca8a04",
      cardBackground: "#fef9c3",
      border: "#fef08a",
    },
  },
  {
    name: "Cinza Elegante",
    value: "elegant-gray",
    colors: {
      background: "#f9fafb",
      text: "#111827",
      primary: "#6b7280",
      primaryHover: "#4b5563",
      cardBackground: "#f3f4f6",
      border: "#d1d5db",
    },
  },
  {
    name: "Roxo",
    value: "purple",
    colors: {
      background: "#faf5ff",
      text: "#581c87",
      primary: "#9333ea",
      primaryHover: "#7c3aed",
      cardBackground: "#f3e8ff",
      border: "#e9d5ff",
    },
  },
  {
    name: "Rosa",
    value: "pink",
    colors: {
      background: "#fdf2f8",
      text: "#831843",
      primary: "#ec4899",
      primaryHover: "#db2777",
      cardBackground: "#fce7f3",
      border: "#fbcfe8",
    },
  },
];

export const colorOptions = [
  // Vibrantes
  "#3b82f6",
  "#ef4444",
  "#10b981",
  "#f59e0b",
  "#8b5cf6",
  "#ec4899",
  "#06b6d4",
  "#84cc16",
  "#f97316",
  "#6366f1",
  "#14b8a6",
  "#f43f5e",

  // Adicionais vibrantes
  "#0ea5e9",
  "#22c55e",
  "#eab308",
  "#a855f7",
  "#fb7185",

  // Tons escuros
  "#64748b",
  "#6b7280",
  "#475569",
  "#334155",
  "#1e293b",
  "#0f172a",

  // Pastel
  "#fbcfe8",
  "#bfdbfe",
  "#bbf7d0",
  "#fef3c7",
  "#ddd6fe",
  "#e0f2fe",

  // Suaves e naturais
  "#f5f5f4",
  "#e7e5e4",
  "#d6d3d1",
  "#a8a29e",
  "#78716c",
  "#57534e",

  // Neons
  "#00f0ff",
  "#ff00c8",
  "#39ff14",
  "#ff3131",
  "#ffd700",
  "#8aff00",

  // Metálicos / Luxo
  "#d4af37",
  "#c0c0c0",
  "#b87333",
  "#a9a9a9",
  "#999999",
  "#343434",
];

export const iconOptions = [
  "Github",
  "Linkedin",
  "Twitter",
  "Instagram",
  "Youtube",
  "Facebook",
  "Mail",
  "Phone",
  "MapPin",
  "Globe",
  "Briefcase",
  "FileText",
  "Music",
  "Camera",
  "Coffee",
  "Heart",
  "Star",
  "BookOpen",
  "Code",
  "Database",
  "Server",
  "Cloud",
  "Lock",
  "Shield",
  "Zap",
  "Battery",
  "Wifi",
  "Bluetooth",
  "Usb",
  "Monitor",
];
