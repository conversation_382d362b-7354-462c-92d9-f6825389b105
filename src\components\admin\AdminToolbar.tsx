import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { User, Link as LinkIcon, Share2, <PERSON>lette, FileText } from "lucide-react";

interface AdminToolbarProps {
  children: React.ReactNode;
}

const AdminToolbar: React.FC<AdminToolbarProps> = ({ children }) => {
  return (
    <Tabs defaultValue="userInfo" className="w-full">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="userInfo" className="flex items-center gap-2">
          <User className="w-4 h-4" />
          Perfil
        </TabsTrigger>
        <TabsTrigger value="links" className="flex items-center gap-2">
          <LinkIcon className="w-4 h-4" />
          Links
        </TabsTrigger>
        <TabsTrigger value="social" className="flex items-center gap-2">
          <Share2 className="w-4 h-4" />
          Redes
        </TabsTrigger>
        <TabsTrigger value="styles" className="flex items-center gap-2">
          <Palette className="w-4 h-4" />
          Estilos
        </TabsTrigger>
        <TabsTrigger value="pages" className="flex items-center gap-2">
          <FileText className="w-4 h-4" />
          Páginas
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
};

export default AdminToolbar;
