// Classe principal do gerenciador de página de links
class LinkPageManager {
    constructor() {
        this.data = null;
        this.currentPage = 'links';
        this.init();
    }

    // Inicialização do aplicativo
    async init() {
        try {
            await this.loadData();
            this.setupEventListeners();
            this.renderPage();
            this.hideLoading();
        } catch (error) {
            this.handleError(error);
        }
    }

    // Carregar dados do JSON
    async loadData() {
        try {
            const response = await fetch('data/links.json');
            if (!response.ok) {
                throw new Error(`Erro ao carregar dados: ${response.status}`);
            }
            this.data = await response.json();
            this.applyTheme();
            this.updateSEO();
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            throw error;
        }
    }

    // Aplicar tema personalizado
    applyTheme() {
        if (!this.data || !this.data.styles) return;

        const styles = this.data.styles;
        const root = document.documentElement;

        // Support both flat and nested color structures
        const colors = styles.colors || styles;

        // Aplicar cores personalizadas
        const primaryColor = styles.primaryColor || colors.primary;
        if (primaryColor) {
            root.style.setProperty('--primary-color', primaryColor);
            root.style.setProperty('--primary-hover', this.adjustBrightness(primaryColor, -20));
        }

        const backgroundColor = styles.backgroundColor || colors.background || colors.cardBackground;
        if (backgroundColor) {
            root.style.setProperty('--background-color', backgroundColor);
        }

        const textColor = styles.textColor || colors.text;
        if (textColor) {
            root.style.setProperty('--text-primary', textColor);
        }

        // Apply border color if available
        const borderColor = colors.border;
        if (borderColor) {
            root.style.setProperty('--border-color', borderColor);
        }

        // Aplicar tema
        if (styles.theme) {
            document.body.setAttribute('data-theme', styles.theme);
        }

        // Aplicar classe de tema personalizado
        if (styles.themeClass) {
            document.body.classList.add(styles.themeClass);
        }

        // Apply layout styles if available
        if (styles.layout) {
            if (styles.layout.borderRadius) {
                root.style.setProperty('--border-radius', styles.layout.borderRadius);
            }
        }
    }

    // Atualizar metadados SEO
    updateSEO() {
        if (!this.data || !this.data.seo) return;

        const seo = this.data.seo;

        // Atualizar título e descrição
        if (seo.title) {
            document.title = seo.title;
            document.getElementById('pageTitle').textContent = seo.title;
            document.getElementById('ogTitle').content = seo.title;
            document.getElementById('twitterTitle').content = seo.title;
        }

        if (seo.description) {
            document.getElementById('pageDescription').content = seo.description;
            document.getElementById('ogDescription').content = seo.description;
            document.getElementById('twitterDescription').content = seo.description;
        }

        if (seo.keywords) {
            document.getElementById('pageKeywords').content = seo.keywords;
        }

        if (seo.author) {
            document.getElementById('pageAuthor').content = seo.author;
        }

        // Atualizar URLs
        const currentUrl = window.location.href;
        document.getElementById('ogUrl').content = currentUrl;
        document.getElementById('twitterUrl').content = currentUrl;

        // Atualizar imagem
        if (seo.image) {
            document.getElementById('ogImage').content = seo.image;
            document.getElementById('twitterImage').content = seo.image;
        }

        // Atualizar favicon
        if (seo.favicon) {
            document.getElementById('favicon').href = seo.favicon;
        }

        // Atualizar rodapé
        if (this.data.profile && this.data.profile.name) {
            document.getElementById('footerTitle').textContent = this.data.profile.name;
        }
    }

    // Configurar event listeners
    setupEventListeners() {
        // Navegação
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateToPage(page);
            });
        });

        // Links do rodapé
        document.querySelectorAll('.footer-links a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                const page = href.substring(1); // Remove o #
                this.navigateToPage(page);
            });
        });

        // Atualizar ano no rodapé
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Fechar loading overlay ao clicar
        document.getElementById('loadingOverlay').addEventListener('click', () => {
            this.hideLoading();
        });
    }

    // Navegar para uma página específica
    navigateToPage(page) {
        if (this.currentPage === page) return;

        // Esconder página atual
        document.getElementById(`${this.currentPage}-page`).classList.remove('active');
        document.querySelector(`.nav-link[data-page="${this.currentPage}"]`).classList.remove('active');

        // Mostrar nova página
        document.getElementById(`${page}-page`).classList.add('active');
        document.querySelector(`.nav-link[data-page="${page}"]`).classList.add('active');

        this.currentPage = page;

        // Renderizar conteúdo da página
        this.renderPage();
    }

    // Renderizar conteúdo da página atual
    renderPage() {
        switch (this.currentPage) {
            case 'links':
                this.renderLinksPage();
                break;
            case 'privacy':
                this.renderPrivacyPage();
                break;
            case 'terms':
                this.renderTermsPage();
                break;
        }
    }

    // Renderizar página de links
    renderLinksPage() {
        if (!this.data) return;

        const profileSection = document.getElementById('profileSection');
        const linksSection = document.getElementById('linksSection');
        const socialSection = document.getElementById('socialSection');

        // Renderizar perfil
        this.renderProfile(profileSection);

        // Renderizar links
        this.renderLinks(linksSection);

        // Renderizar redes sociais
        this.renderSocial(socialSection);
    }

    // Renderizar seção de perfil
    renderProfile(container) {
        // Support both 'profile' and 'userInfo' for backward compatibility
        const profile = this.data.profile || this.data.userInfo;
        if (!profile) return;

        let avatarHTML = '';

        if (profile.avatar) {
            avatarHTML = `<img src="${profile.avatar}" alt="${profile.name || 'Avatar'}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`;
            avatarHTML += `<i class="fas fa-user" style="display: none;"></i>`;
        } else {
            avatarHTML = '<i class="fas fa-user"></i>';
        }

        container.innerHTML = `
            <div class="profile-avatar fade-in">
                ${avatarHTML}
            </div>
            ${profile.name ? `<h1 class="profile-name fade-in">${this.escapeHtml(profile.name)}</h1>` : ''}
            ${profile.bio ? `<p class="profile-bio fade-in">${this.escapeHtml(profile.bio)}</p>` : ''}
        `;
    }

    // Renderizar links
    renderLinks(container) {
        if (!this.data.links || !Array.isArray(this.data.links)) return;

        // Filter only enabled links
        const enabledLinks = this.data.links.filter(link => link.enabled !== false);

        container.innerHTML = enabledLinks.map((link, index) => {
            const icon = this.getLinkIcon(link.icon);
            const animationDelay = index * 0.1;

            return `
                <a href="${this.escapeHtml(link.url)}"
                   class="link-item slide-up"
                   target="_blank"
                   rel="noopener noreferrer"
                   style="animation-delay: ${animationDelay}s">
                    ${icon}
                    <span>${this.escapeHtml(link.title)}</span>
                </a>
            `;
        }).join('');
    }

    // Renderizar redes sociais
    renderSocial(container) {
        // Support both 'social' and 'socialMedia' for backward compatibility
        const socialData = this.data.social || this.data.socialMedia;
        if (!socialData || !Array.isArray(socialData)) return;

        // Filter only enabled social links
        const enabledSocial = socialData.filter(social => social.enabled !== false);

        container.innerHTML = enabledSocial.map((social, index) => {
            const icon = this.getSocialIcon(social.platform);
            const animationDelay = index * 0.1;

            return `
                <a href="${this.escapeHtml(social.url)}"
                   class="social-link slide-up"
                   target="_blank"
                   rel="noopener noreferrer"
                   style="animation-delay: ${animationDelay}s"
                   title="${this.escapeHtml(social.platform)}">
                    ${icon}
                </a>
            `;
        }).join('');
    }

    // Renderizar página de privacidade
    renderPrivacyPage() {
        const container = document.getElementById('privacyContent');

        if (!this.data.privacyPolicy) {
            container.innerHTML = `
                <div class="legal-content">
                    <h1>Política de Privacidade</h1>
                    <p>Política de privacidade não disponível no momento.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="legal-content fade-in">
                <h1>${this.escapeHtml(this.data.privacyPolicy.title || 'Política de Privacidade')}</h1>
                ${this.formatLegalContent(this.data.privacyPolicy.content || '')}
            </div>
        `;
    }

    // Renderizar página de termos
    renderTermsPage() {
        const container = document.getElementById('termsContent');

        if (!this.data.termsOfService) {
            container.innerHTML = `
                <div class="legal-content">
                    <h1>Termos de Uso</h1>
                    <p>Termos de uso não disponíveis no momento.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="legal-content fade-in">
                <h1>${this.escapeHtml(this.data.termsOfService.title || 'Termos de Uso')}</h1>
                ${this.formatLegalContent(this.data.termsOfService.content || '')}
            </div>
        `;
    }

    // Format conteúdo legal (converter markdown-like para HTML)
    formatLegalContent(content) {
        if (!content) return '<p>Conteúdo não disponível.</p>';

        let formatted = this.escapeHtml(content);

        // Converter títulos
        formatted = formatted.replace(/^### (.*$)/gim, '<h3>$1</h3>');
        formatted = formatted.replace(/^## (.*$)/gim, '<h2>$1</h2>');
        formatted = formatted.replace(/^# (.*$)/gim, '<h1>$1</h1>');

        // Converter listas
        formatted = formatted.replace(/^\* (.*$)/gim, '<li>$1</li>');
        formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

        // Converter parágrafos
        formatted = formatted.replace(/\n\n/g, '</p><p>');
        formatted = '<p>' + formatted + '</p>';

        // Converter links
        formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

        // Converter negrito
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Converter itálico
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

        return formatted;
    }

    // Obter ícone para links
    getLinkIcon(iconName) {
        if (!iconName) return '<i class="fas fa-link"></i>';

        const normalizedIcon = iconName.toLowerCase();
        const icons = {
            'home': '<i class="fas fa-home"></i>',
            'user': '<i class="fas fa-user"></i>',
            'envelope': '<i class="fas fa-envelope"></i>',
            'email': '<i class="fas fa-envelope"></i>',
            'phone': '<i class="fas fa-phone"></i>',
            'external-link-alt': '<i class="fas fa-external-link-alt"></i>',
            'link': '<i class="fas fa-link"></i>',
            'camera': '<i class="fas fa-camera"></i>',
            'github': '<i class="fab fa-github"></i>',
            'linkedin': '<i class="fab fa-linkedin"></i>',
            'filetext': '<i class="fas fa-file-text"></i>',
            'file-text': '<i class="fas fa-file-text"></i>',
            'blog': '<i class="fas fa-blog"></i>',
            'portfolio': '<i class="fas fa-briefcase"></i>',
            'website': '<i class="fas fa-globe"></i>'
        };

        return icons[normalizedIcon] || '<i class="fas fa-link"></i>';
    }

    // Obter ícone para redes sociais
    getSocialIcon(platform) {
        const socialIcons = {
            'facebook': '<i class="fab fa-facebook-f"></i>',
            'twitter': '<i class="fab fa-twitter"></i>',
            'instagram': '<i class="fab fa-instagram"></i>',
            'linkedin': '<i class="fab fa-linkedin-in"></i>',
            'youtube': '<i class="fab fa-youtube"></i>',
            'tiktok': '<i class="fab fa-tiktok"></i>',
            'github': '<i class="fab fa-github"></i>',
            'whatsapp': '<i class="fab fa-whatsapp"></i>',
            'telegram': '<i class="fab fa-telegram"></i>',
            'discord': '<i class="fab fa-discord"></i>',
            'website': '<i class="fas fa-globe"></i>',
            'blog': '<i class="fas fa-blog"></i>',
            'portfolio': '<i class="fas fa-briefcase"></i>',
            'contact': '<i class="fas fa-envelope"></i>',
            'email': '<i class="fas fa-envelope"></i>',
            'mail': '<i class="fas fa-envelope"></i>',
            'phone': '<i class="fas fa-phone"></i>',
            'mobile': '<i class="fas fa-mobile"></i>'
        };

        return socialIcons[platform.toLowerCase()] || '<i class="fas fa-link"></i>';
    }

    // Ajustar brilho da cor
    adjustBrightness(hex, percent) {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    // Escapar HTML para prevenir XSS
    escapeHtml(text) {
        if (!text) return '';
        if (typeof text !== 'string') text = String(text);

        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // Mostrar loading
    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    // Esconder loading
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    // Tratar erros
    handleError(error) {
        console.error('Erro:', error);
        this.hideLoading();

        // Mostrar mensagem de erro amigável
        const errorHtml = `
            <div class="error-message">
                <h2>Ops! Algo deu errado.</h2>
                <p>Não foi possível carregar os dados. Por favor, tente novamente mais tarde.</p>
                <button onclick="location.reload()">Recarregar Página</button>
            </div>
        `;

        document.querySelector('.main-content').innerHTML = errorHtml;
    }
}

// Inicializar o aplicativo quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    new LinkPageManager();
});