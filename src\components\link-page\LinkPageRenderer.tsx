'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Link, Github, Linkedin, Twitter, Instagram, Youtube, FileText, Briefcase, ExternalLink } from 'lucide-react'

interface LinkItem {
  id: string
  title: string
  url: string
  icon?: string
  enabled: boolean
}

interface SocialMediaItem {
  id: string
  platform: string
  url: string
  icon: string
  enabled: boolean
}

interface UserInfo {
  name: string
  bio: string
  avatar: string
}

interface Styles {
  theme: string
  colors: {
    background: string
    text: string
    primary: string
    primaryHover: string
    cardBackground: string
    border: string
  }
  typography: {
    fontFamily: string
    fontSize: string
    headingSize: string
  }
  layout: {
    borderRadius: string
    spacing: string
    maxWidth: string
  }
  background: {
    type: string
    value: string
    image: string
  }
}

interface LinkPageData {
  userInfo: UserInfo
  links: LinkItem[]
  socialMedia: SocialMediaItem[]
  styles: Styles
  seo: {
    title: string
    description: string
    keywords: string
    favicon: string
    shareImage: string
  }
}

interface LinkPageRendererProps {
  data: LinkPageData
}

const iconComponents: { [key: string]: React.ComponentType<any> } = {
  Link,
  Github,
  Linkedin,
  Twitter,
  Instagram,
  Youtube,
  FileText,
  Briefcase,
  ExternalLink
}

export default function LinkPageRenderer({ data }: LinkPageRendererProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  const { userInfo, links, socialMedia, styles } = data

  const getIconComponent = (iconName: string) => {
    return iconComponents[iconName] || Link
  }

  const enabledLinks = links.filter(link => link.enabled)
  const enabledSocialMedia = socialMedia.filter(social => social.enabled)

  return (
    <div 
      className="min-h-screen flex items-center justify-center p-4 sm:p-6 md:p-8 transition-colors duration-300 relative overflow-hidden"
      style={{
        backgroundColor: styles.colors.background,
        backgroundImage: styles.background.type === 'image' ? `url(${styles.background.image})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        fontFamily: styles.typography.fontFamily
      }}
    >
      {/* Background Pattern Overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="relative w-full max-w-md sm:max-w-lg md:max-w-xl">
        <Card 
          className="shadow-2xl backdrop-blur-sm border-0 overflow-hidden"
          style={{
            backgroundColor: styles.colors.cardBackground + '95',
            borderColor: styles.colors.border,
            borderRadius: styles.layout.borderRadius,
            maxWidth: styles.layout.maxWidth
          }}
        >
          <CardContent className="p-6 sm:p-8 space-y-6">
            {/* Profile Section */}
            <div className="text-center space-y-4">
              <div className="relative inline-block">
                <div className="group">
                  <Avatar className="w-20 h-20 sm:w-24 sm:h-24 mx-auto ring-4 ring-white/20 shadow-lg transition-transform group-hover:scale-105">
                    <AvatarImage src={userInfo.avatar} alt={userInfo.name} />
                    <AvatarFallback className="text-xl sm:text-2xl bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      {userInfo.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow-md"></div>
              </div>
              
              <div className="space-y-2">
                <h1 
                  className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent"
                  style={{ 
                    color: styles.colors.text,
                    fontSize: styles.typography.headingSize 
                  }}
                >
                  {userInfo.name}
                </h1>
                <p 
                  className="text-sm sm:text-base leading-relaxed text-gray-600"
                  style={{ color: styles.colors.text }}
                >
                  {userInfo.bio}
                </p>
              </div>
            </div>

            <div>
              <Separator style={{ backgroundColor: styles.colors.border }} />
            </div>

            {/* Links Section */}
            <div className="space-y-3">
              {enabledLinks.map((link) => {
                const Icon = getIconComponent(link.icon || 'Link')
                return (
                  <Button
                    key={link.id}
                    variant="outline"
                    className="w-full justify-start gap-3 h-auto py-3 px-4 transition-all duration-200 relative overflow-hidden group"
                    style={{
                      backgroundColor: styles.colors.cardBackground,
                      borderColor: styles.colors.border,
                      color: styles.colors.text
                    }}
                    asChild
                  >
                    <a 
                      href={link.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 w-full relative z-10"
                    >
                      <Icon className="w-5 h-5 flex-shrink-0 transition-transform group-hover:scale-110" style={{ color: styles.colors.primary }} />
                      <span className="font-medium text-left flex-1">{link.title}</span>
                      <ExternalLink className="w-4 h-4 flex-shrink-0 opacity-60 transition-transform group-hover:translate-x-1" />
                      
                      {/* Hover Effect Background */}
                      <div 
                        className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 -z-10"
                        style={{ backgroundColor: styles.colors.primary + '10' }}
                      />
                    </a>
                  </Button>
                )
              })}
            </div>

            {/* Social Media Section */}
            {enabledSocialMedia.length > 0 && (
              <>
                <div>
                  <Separator style={{ backgroundColor: styles.colors.border }} />
                </div>
                
                <div className="space-y-3">
                  <h3 
                    className="text-xs sm:text-sm font-semibold text-center uppercase tracking-wider"
                    style={{ color: styles.colors.text }}
                  >
                    Redes Sociais
                  </h3>
                  <div className="flex justify-center gap-2 sm:gap-3 flex-wrap">
                    {enabledSocialMedia.map((social) => {
                      const Icon = getIconComponent(social.icon)
                      return (
                        <Button
                          key={social.id}
                          size="sm"
                          variant="outline"
                          className="rounded-full p-2 h-10 w-10 sm:h-11 sm:w-11 shadow-md transition-all duration-200 hover:scale-110 hover:-translate-y-1"
                          style={{
                            backgroundColor: styles.colors.cardBackground,
                            borderColor: styles.colors.border,
                            color: styles.colors.text
                          }}
                          asChild
                        >
                          <a 
                            href={social.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="flex items-center justify-center"
                            title={social.platform}
                          >
                            <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                          </a>
                        </Button>
                      )
                    })}
                  </div>
                </div>
              </>
            )}

            {/* Footer Links */}
            <div className="flex justify-center gap-3 sm:gap-4 pt-4">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs opacity-70 hover:opacity-100 transition-opacity"
                style={{ color: styles.colors.text }}
                asChild
              >
                <a href="/privacy">Privacidade</a>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs opacity-70 hover:opacity-100 transition-opacity"
                style={{ color: styles.colors.text }}
                asChild
              >
                <a href="/terms">Termos</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}